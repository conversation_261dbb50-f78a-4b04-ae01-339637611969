[project]
name = "arbor-ai"
version = "0.2.5"
description = "A framework for fine-tuning and managing language models"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "torch>=2.7.0",
    "fastapi",
    "uvicorn",
    "click",
    "python-multipart",
    "pydantic-settings",
    "vllm>=0.9.1",
    "transformers",
    "trl==0.19.1",
    "peft",
    "ray>=2.9",
    "setuptools (>=76.0.0,<79.0.1)",
    "pyzmq>=26.4.0",
    "pyyaml>=6.0.2",
    "wandb",
    "httpx",
    "coolname", # Used for generating slugs in job ids
    "jq>=1.8.0",
    "textual", 
    "verifiers", # Lots of our code is adopted from <PERSON>'s Verifiers library, so we give credit here
]

[project.scripts]
arbor = "arbor.cli:cli"

[dependency-groups]
dev = [
    "black",
    "pre-commit",
    "isort",
    "pytest",
    "openai"
]

[tool.setuptools.packages.find]
where = ["."]
include = ["arbor", "arbor.*"]
exclude = ["tests", "tests.*"]

[project.urls]
Homepage = "https://github.com/Ziems/arbor"
Issues = "https://github.com/Ziems/arbor/issues"
