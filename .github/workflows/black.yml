name: Check code formatting with Black

on:
  pull_request:
    paths:
      - '**.py'
      - 'pyproject.toml'

jobs:
  black-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'  # or your project's version

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black

      - name: Check formatting with Black
        run: black --check .