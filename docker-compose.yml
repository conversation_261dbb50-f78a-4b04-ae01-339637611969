version: '3.8'

services:
  arbor:
    build: .
    ports:
      - "7453:7453"
    volumes:
      - ~/.arbor:/root/.arbor
    environment:
      - CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-all}
      - NCCL_P2P_DISABLE=${NCCL_P2P_DISABLE:-0}
      - NCCL_IB_DISABLE=${NCCL_IB_DISABLE:-0}
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7453/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s