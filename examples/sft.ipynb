{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Assumes that the server is running\n", "from openai import OpenAI\n", "\n", "arbor_port = 7453\n", "\n", "client = OpenAI(\n", "    base_url=f\"http://127.0.0.1:{arbor_port}/v1\",  # Using Arbor server\n", "    api_key=\"not-needed\",  # If you're using a local server, you dont need an API key\n", ")\n", "\n", "\n", "response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen2-0.5B-Instruct\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Hello, how are you?\"},\n", "    ],\n", "    temperature=0.7,\n", ")\n", "print(response)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "file-8f919b22-43c3-4690-a448-376a65bc68b1\n"]}], "source": ["import requests\n", "import json\n", "\n", "arbor_port = 7453\n", "\n", "def upload_file_oai(file_path):\n", "    return client.files.create(\n", "        file=open(file_path, 'rb'),\n", "        purpose='fine-tune'\n", "    )\n", "\n", "def upload_file(file_path, url=f'http://127.0.0.1:{arbor_port}/v1/files'):\n", "    \"\"\"\n", "    Upload a file to the specified URL endpoint.\n", "\n", "    Args:\n", "        file_path (str): Path to the file to be uploaded\n", "        url (str): URL endpoint for file upload (default: http://127.0.0.1:{arbor_port}/api/files)\n", "\n", "    Returns:\n", "        requests.Response: Response from the server\n", "    \"\"\"\n", "    with open(file_path, 'rb') as file:\n", "        files = {'file': file}\n", "        response = requests.post(url, files=files)\n", "    return response\n", "\n", "# Example usage:\n", "method = 'sft'\n", "response = upload_file(f'../tests/data/training_data_{method}.jsonl')\n", "# response = upload_file_oai('../tests/data/training_data_sft.jsonl')\n", "print(response.status_code)  # Print the HTTP status code\n", "response_body = json.loads(response.text)\n", "uploaded_file = response_body[\"id\"]\n", "print(uploaded_file)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FineTuningJob(id='ftjob-a8a5ed87-9994-4e3f-a97a-235e852187df', created_at=None, error=None, fine_tuned_model=None, finished_at=None, hyperparameters=None, model=None, object='fine_tuning.job', organization_id=None, result_files=None, seed=None, status='queued', trained_tokens=None, training_file=None, validation_file=None, estimated_finish=None, integrations=None, metadata=None, method=None)\n"]}], "source": ["#\"HuggingFaceTB/SmolLM2-135M-Instruct\"\n", "#\"Qwen/Qwen2-0.5B-Instruct\"\n", "def start_fine_tune(training_file, model_name=\"Qwen/Qwen2-0.5B-Instruct\"):\n", "    return client.fine_tuning.jobs.create(\n", "        method={\"type\": method},\n", "        training_file=training_file,\n", "        model=model_name,\n", "    )\n", "\n", "# Example usage (continuing from previous upload):\n", "fine_tune_response = start_fine_tune(uploaded_file)\n", "print(fine_tune_response)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "{\"object\":\"fine_tuning.job\",\"id\":\"ftjob-0eedff0f-26e8-4bd2-9520-c9dd1919aab2\",\"fine_tuned_model\":null,\"status\":\"queued\"}\n"]}, {"data": {"text/plain": ["<Response [200]>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["def check_job_status_oai(job_id):\n", "    return client.fine_tuning.jobs.retrieve(job_id)\n", "\n", "\n", "def check_job_status(job_id, url_base=f'http://127.0.0.1:7453/v1/fine_tuning/jobs'):\n", "    \"\"\"\n", "    Check the status of a fine-tuning job.\n", "\n", "    Args:\n", "        job_id (str): ID of the fine-tuning job\n", "        url_base (str): Base URL for job status endpoint (default: http://127.0.0.1:8000/api/job)\n", "\n", "    Returns:\n", "        requests.Response: Response from the server\n", "    \"\"\"\n", "    url = f\"{url_base}/{job_id}\"\n", "    response = requests.get(url)\n", "    return response\n", "\n", "# Example usage (continuing from previous fine-tune):\n", "job_response = check_job_status(fine_tune_response.id)\n", "print(job_response.status_code)\n", "print(job_response.text)\n", "job_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 2}