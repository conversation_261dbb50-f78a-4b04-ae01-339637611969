# Git
.git
.gitignore

# Documentation
*.md
LICENSE
TODO.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Jupyter
.ipynb_checkpoints

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Examples and tests (optional - uncomment if you want to exclude)
# examples/
# tests/

# Storage and logs
storage/
logs/
*.log