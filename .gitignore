# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/

# Virtual Environment
.env
.*venv/
env/
venv/
ENV/
.*conda/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Project specific
/uploads/
/models/
*.log
/test_storage/
/storage/
/wandb/

# OS specific
.DS_Store
Thumbs.db

mise.toml
uv.lock
